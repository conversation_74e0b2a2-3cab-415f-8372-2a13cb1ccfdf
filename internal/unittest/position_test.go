package unittest

import (
	"context"
	"encoding/json"
	"testing"

	"futures-asset/configs"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	mockRepo "futures-asset/internal/mock/repository"
	usecaseImpl "futures-asset/internal/usecase"
	"futures-asset/internal/utils"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"
	cfg "yt.com/backend/common.git/config"

	futuresEnginePB "yt.com/backend/common.git/business/grpc/gen/futures/engine/v1"
)

// PositionUseCaseTestSuite 仓位用例测试套件
type PositionUseCaseTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller

	// Mock repositories
	mockPositionRepo *mockRepo.MockPositionRepository
	mockCacheRepo    *mockRepo.MockCacheRepository
	mockAssetRepo    *mockRepo.MockAssetRepository
	mockPriceRepo    *mockRepo.MockPriceRepository

	// Use case under test
	positionUseCase usecase.PositionUseCase

	// Test context
	ctx context.Context
}

// SetupSuite 设置测试套件
func (suite *PositionUseCaseTestSuite) SetupSuite() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.ctx = context.Background()

	// 创建 mock repositories
	suite.mockPositionRepo = mockRepo.NewMockPositionRepository(suite.ctrl)
	suite.mockCacheRepo = mockRepo.NewMockCacheRepository(suite.ctrl)
	suite.mockAssetRepo = mockRepo.NewMockAssetRepository(suite.ctrl)
	suite.mockPriceRepo = mockRepo.NewMockPriceRepository(suite.ctrl)

	// 创建配置
	config := &cfg.Config[configs.Config]{}

	// 创建 PositionUseCase 实例
	param := usecaseImpl.PositionUseCaseParam{
		Config:       config,
		PositionRepo: suite.mockPositionRepo,
		CacheRepo:    suite.mockCacheRepo,
		AssetRepo:    suite.mockAssetRepo,
	}

	suite.positionUseCase = usecaseImpl.NewPositionUseCase(param)
}

// TearDownSuite 清理测试套件
func (suite *PositionUseCaseTestSuite) TearDownSuite() {
	suite.ctrl.Finish()
}

// TestPositionUseCaseTestSuite 运行测试套件
func TestPositionUseCaseTestSuite(t *testing.T) {
	suite.Run(t, new(PositionUseCaseTestSuite))
}

// ==================== 查询类方法测试 ====================

// TestUserPos 测试用户仓位查询
func (suite *PositionUseCaseTestSuite) TestUserPos() {
	// 准备测试数据
	req := &repository.SwapParam{
		UID:    "test_user_123",
		Symbol: "BTC-USDT",
	}

	expectedPositions := []repository.PosSwap{
		{
			UID:      "test_user_123",
			Symbol:   "BTC-USDT",
			Pos:      decimal.NewFromFloat(1.5),
			PosSide:  1, // 多仓
			Currency: "USDT",
		},
		{
			UID:      "test_user_123",
			Symbol:   "BTC-USDT",
			Pos:      decimal.NewFromFloat(0.8),
			PosSide:  2, // 空仓
			Currency: "USDT",
		},
	}

	// 设置 mock 期望
	suite.mockPositionRepo.EXPECT().
		UserPos(suite.ctx, req).
		Return(expectedPositions, nil).
		Times(1)

	// 执行测试
	result, err := suite.positionUseCase.UserPos(suite.ctx, req)

	// 验证结果
	suite.NoError(err, "靠,不应该有错误")
	suite.Equal(len(expectedPositions), len(result), "返回的仓位数量应该匹配")
	suite.Equal(expectedPositions[0].UID, result[0].UID, "第一个仓位的用户ID应该匹配")
	suite.Equal(expectedPositions[0].Pos, result[0].Pos, "第一个仓位的数量应该匹配")
}

// TestQueryUserPos 测试查询用户仓位
func (suite *PositionUseCaseTestSuite) TestQueryUserPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestQueryUserPos - 待实现")
}

// TestPosInfo 测试仓位信息查询
func (suite *PositionUseCaseTestSuite) TestPosInfo() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestPosInfo - 待实现")
}

// TestPosTotal 测试仓位总量查询
func (suite *PositionUseCaseTestSuite) TestPosTotal() {
	// 准备测试数据
	contractCode := "BTC-USDT"
	expectedTotal := decimal.NewFromFloat(100.5)

	// 设置 mock 期望
	suite.mockPositionRepo.EXPECT().
		PosTotal(suite.ctx, contractCode).
		Return(expectedTotal).
		Times(1)

	// 执行测试
	result := suite.positionUseCase.PosTotal(suite.ctx, contractCode)

	// 验证结果
	suite.Equal(expectedTotal, result, "仓位总量应该匹配期望值")
}

// TestUserHoldPos 测试用户持仓查询
func (suite *PositionUseCaseTestSuite) TestUserHoldPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestUserHoldPos - 待实现")
}

// TestPlatPosList 测试平台仓位列表查询
func (suite *PositionUseCaseTestSuite) TestPlatPosList() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestPlatPosList - 待实现")
}

// TestPlatPosDetail 测试平台仓位详情查询
func (suite *PositionUseCaseTestSuite) TestPlatPosDetail() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestPlatPosDetail - 待实现")
}

// ==================== 开仓操作测试 ====================

// TestOpenLongPos 测试开多仓
func (suite *PositionUseCaseTestSuite) TestOpenLongPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestOpenLongPos - 待实现")
}

// TestOpenShortPos 测试开空仓
func (suite *PositionUseCaseTestSuite) TestOpenShortPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestOpenShortPos - 待实现")
}

// TestOpenBothPos 测试开单向仓
func (suite *PositionUseCaseTestSuite) TestOpenBothPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestOpenBothPos - 待实现")
}

// ==================== 平仓操作测试 ====================

// TestCloseLongPos 测试平多仓
func (suite *PositionUseCaseTestSuite) TestCloseLongPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestCloseLongPos - 待实现")
}

// TestCloseShortPos 测试平空仓
func (suite *PositionUseCaseTestSuite) TestCloseShortPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestCloseShortPos - 待实现")
}

// TestCloseBothPos 测试平单向仓
func (suite *PositionUseCaseTestSuite) TestCloseBothPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestCloseBothPos - 待实现")
}

// ==================== 辅助方法 ====================

// createMockUserAsset 创建模拟用户资产
func (suite *PositionUseCaseTestSuite) createMockUserAsset(userAssetJson string) *repository.AssetSwap {
	var userAsset repository.AssetSwap
	json.Unmarshal([]byte(userAssetJson), &userAsset)
	// return &repository.AssetSwap{
	// 	UID: "test_user_123",
	// 	LongPos: repository.PosSwap{
	// 		UID:            "test_user_123",
	// 		PosId:          "long_pos_123",
	// 		Symbol:         "BTC-USDT",
	// 		Currency:       "USDT",
	// 		Pos:            decimal.Zero,
	// 		PosAvailable:   decimal.Zero,
	// 		OpenPriceAvg:   decimal.Zero,
	// 		IsolatedMargin: decimal.Zero,
	// 		Leverage:       10,
	// 		MarginMode:     1, // 逐仓
	// 		PosSide:        1, // 多仓
	// 	},
	// 	ShortPos: repository.PosSwap{
	// 		UID:            "test_user_123",
	// 		PosId:          "short_pos_123",
	// 		Symbol:         "BTC-USDT",
	// 		Currency:       "USDT",
	// 		Pos:            decimal.Zero,
	// 		PosAvailable:   decimal.Zero,
	// 		OpenPriceAvg:   decimal.Zero,
	// 		IsolatedMargin: decimal.Zero,
	// 		Leverage:       10,
	// 		MarginMode:     1, // 逐仓
	// 		PosSide:        2, // 空仓
	// 	},
	// 	BothPos: repository.PosSwap{
	// 		UID:            "test_user_123",
	// 		PosId:          "both_pos_123",
	// 		Symbol:         "BTC-USDT",
	// 		Currency:       "USDT",
	// 		Pos:            decimal.Zero,
	// 		PosAvailable:   decimal.Zero,
	// 		OpenPriceAvg:   decimal.Zero,
	// 		IsolatedMargin: decimal.Zero,
	// 		Leverage:       10,
	// 		MarginMode:     1, // 逐仓
	// 		PosSide:        3, // 单向仓
	// 	},
	// 	Balance: map[string]decimal.Decimal{
	// 		"USDT": decimal.NewFromFloat(10000), // 发你 10000 USDT
	// 	},
	// 	Frozen: map[string]decimal.Decimal{
	// 		"USDT": decimal.Zero,
	// 	},
	// }

	return &userAsset
}

// createMockAccountSettleParam 创建模拟账户结算参数
func (suite *PositionUseCaseTestSuite) createMockAccountSettleParam(jsonFilePath string) utils.AccountSettleParam {
	var accountSettleMsg futuresEnginePB.AccountSettleEngine
	json.Unmarshal([]byte(accountSettleMsgJson), &accountSettleMsg)
	return utils.AccountSettleParam{
		AccountSettle: &accountSettleMsg,
	}
}
